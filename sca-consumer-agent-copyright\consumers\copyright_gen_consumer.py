import time
from typing import Dict, Any

from common_config.common_config import RedisKeyConfig
from omni.msg_queue.redis_set_consumer import consume_redis_set
from models.models import CopyrightTask
from agent.graph import graph
from omni.log.log import olog


@consume_redis_set(redis_key=RedisKeyConfig.COPYRIGHT_GEN_SET, num_tasks=10)
async def handle_task(message_data: Dict[str, Any]) -> None:
    """
    处理软件著作权生成任务

    Args:
        message_data: Redis消息数据，包含CopyrightTask的ID
    """
    task = None

    try:
        # 1. 从message_data中提取CopyrightTask的ID
        task_id = message_data["task_id"]
        olog.info(f"开始处理软件著作权生成任务, task_id: {task_id}")

        # 2. 根据ID查询数据库获取CopyrightTask对象
        task = await CopyrightTask.get(task_id)
        olog.info(f"获取到CopyrightTask, name: {task.name}, status: {task.status}")

        # 3. 使用task.name作为user_requirement参数运行graph
        olog.info(f"开始运行graph工作流, user_requirement: {task.name}")
        workflow_result = await graph.ainvoke({"user_requirement": task.name})
        olog.info(f"graph工作流执行成功, 结果: {workflow_result}")

        # 4. 处理运行结果并更新数据库
        oss_key = workflow_result["oss_key"]
        task.status = "已完成"
        task.document_key = oss_key
        task.completed_at = int(time.time())
        await task.save()
        olog.info(f"更新CopyrightTask成功, task_id: {task.id}, status: 已完成, document_key: {oss_key}")

        olog.info(f"软件著作权生成任务处理成功, task_id: {task_id}, oss_key: {oss_key}")

    except Exception as e:
        task_id = message_data.get("task_id", "unknown")
        error_msg = f"软件著作权生成任务处理失败, task_id: {task_id}, 错误: {str(e)}"
        olog.error(error_msg)

        # 如果已经获取到task对象，更新状态为生成失败
        if task:
            try:
                task.status = "生成失败"
                await task.save()
                olog.info(f"已更新任务状态为生成失败, task_id: {task_id}")
            except Exception as update_error:
                olog.error(f"更新任务状态失败, task_id: {task_id}, 错误: {str(update_error)}")

        # 重新抛出异常，让消费者框架处理
        raise
